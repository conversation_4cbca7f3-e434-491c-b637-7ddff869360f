
============================================================
Bootstrap error at 2025-06-17T16:56:06.889633
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found
Traceback (most recent call last):
  File "E:\Webapp Development\services\api\bootstrap.py", line 28, in <module>
    from api.routes import app
  File "E:\Webapp Development\services\api\routes.py", line 37, in <module>
    from solar.access import User
  File "E:\Webapp Development\services\solar\__init__.py", line 1, in <module>
    from .table import Table, ColumnDetails
  File "E:\Webapp Development\services\solar\table.py", line 16, in <module>
    from psycopg.rows import dict_row
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\__init__.py", line 9, in <module>
    from . import pq  # noqa: F401 import early to stabilize side effects
    ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 116, in <module>
    import_from_libpq()
    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 108, in import_from_libpq
            raise ImportError(
    ...<4 lines>...
            )
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found

============================================================
Bootstrap error at 2025-06-17T17:04:30.791754
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found
Traceback (most recent call last):
  File "E:\Webapp Development\services\api\bootstrap.py", line 29, in <module>
    from api.routes import app
  File "E:\Webapp Development\services\api\routes.py", line 37, in <module>
    from solar.access import User
  File "E:\Webapp Development\services\solar\__init__.py", line 1, in <module>
    from .table import Table, ColumnDetails
  File "E:\Webapp Development\services\solar\table.py", line 16, in <module>
    from psycopg.rows import dict_row
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\__init__.py", line 9, in <module>
    from . import pq  # noqa: F401 import early to stabilize side effects
    ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 116, in <module>
    import_from_libpq()
    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 108, in import_from_libpq
            raise ImportError(
    ...<4 lines>...
            )
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Webapp Development\services\api\bootstrap.py", line 34, in <module>
    from api.mock_routes import app
  File "E:\Webapp Development\services\api\mock_routes.py", line 37, in <module>
    from solar.access import User
  File "E:\Webapp Development\services\solar\__init__.py", line 1, in <module>
    from .table import Table, ColumnDetails
  File "E:\Webapp Development\services\solar\table.py", line 16, in <module>
    from psycopg.rows import dict_row
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\__init__.py", line 9, in <module>
    from . import pq  # noqa: F401 import early to stabilize side effects
    ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 116, in <module>
    import_from_libpq()
    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 108, in import_from_libpq
            raise ImportError(
    ...<4 lines>...
            )
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found

============================================================
Bootstrap error at 2025-06-17T17:06:09.297545
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found
Traceback (most recent call last):
  File "E:\Webapp Development\services\api\bootstrap.py", line 29, in <module>
    from api.routes import app
  File "E:\Webapp Development\services\api\routes.py", line 37, in <module>
    from solar.access import User
  File "E:\Webapp Development\services\solar\__init__.py", line 1, in <module>
    from .table import Table, ColumnDetails
  File "E:\Webapp Development\services\solar\table.py", line 16, in <module>
    from psycopg.rows import dict_row
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\__init__.py", line 9, in <module>
    from . import pq  # noqa: F401 import early to stabilize side effects
    ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 116, in <module>
    import_from_libpq()
    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 108, in import_from_libpq
            raise ImportError(
    ...<4 lines>...
            )
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Webapp Development\services\api\bootstrap.py", line 34, in <module>
    from api.mock_routes import app
  File "E:\Webapp Development\services\api\mock_routes.py", line 50, in <module>
    from api.models import TokenExchangeRequest, TokenResponse, TokenValidationRequest, LogoutResponse
  File "E:\Webapp Development\services\api\models.py", line 27, in <module>
    from core.project import Project
  File "E:\Webapp Development\services\core\project.py", line 1, in <module>
    from solar import Table, ColumnDetails
  File "E:\Webapp Development\services\solar\__init__.py", line 1, in <module>
    from .table import Table, ColumnDetails
  File "E:\Webapp Development\services\solar\table.py", line 16, in <module>
    from psycopg.rows import dict_row
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\__init__.py", line 9, in <module>
    from . import pq  # noqa: F401 import early to stabilize side effects
    ^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 116, in <module>
    import_from_libpq()
    ~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\miniconda3\Lib\site-packages\psycopg\pq\__init__.py", line 108, in import_from_libpq
            raise ImportError(
    ...<4 lines>...
            )
ImportError: no pq wrapper available.
Attempts made:
- couldn't import psycopg 'c' implementation: No module named 'psycopg_c'
- couldn't import psycopg 'binary' implementation: No module named 'psycopg_binary'
- couldn't import psycopg 'python' implementation: libpq library not found
INFO  | GET /health (404) - 0.000s
INFO  | GET /health (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET /docs (200) - 0.000s
INFO  | GET /docs (200) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET /favicon.ico (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET / (404) - 0.000s
INFO  | GET / (404) - 0.000s
