
import './logger.ts';
import { createContext, Suspense } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter, Routes, Route } from "react-router-dom";

import SystemErrorBoundary from "./SystemErrorBoundary.tsx";
import "./index.css";
import ScreenshotComponent from "./components/ScreenshotComponent.tsx";
import Router from "./components/Router.tsx";
import { GoogleAuthProvider, GoogleSignedIn, GoogleSignedOut } from "./auth/GoogleAuthProvider.tsx";
import { GoogleLoginPage } from "./auth/GoogleLoginPage.tsx";
import { ThemeProvider } from "./components/ThemeProvider.tsx";
import { client } from './lib/sdk/client.gen';

client.setConfig({
  baseUrl: import.meta.env.VITE_API_BASE_URL || "http://localhost:8000",
});

export const AuthTokenContext = createContext<string | null>(null);

const Root = () => {
  console.log('Root component rendering...');
  Element.prototype.scrollIntoView = function() { return false; };
  Element.prototype.scrollTo = function() { return false; };
  Element.prototype.scrollBy = function() { return false; };

  // Full application without ScreenshotComponent
  return (
    <ThemeProvider defaultTheme={{ base: "dark", color: "default" }}>
      <GoogleAuthProvider
        client={client}
        appName={import.meta.env.VITE_APP_NAME || "Veo 3 Prompt Architect"}
      >
        <BrowserRouter>
          <GoogleSignedOut>
            <GoogleLoginPage />
          </GoogleSignedOut>

          <GoogleSignedIn>
            <div style={{
              padding: '20px',
              fontFamily: 'Arial, sans-serif',
              minHeight: '100vh'
            }}>
              <h1>🎉 You're Signed In!</h1>
              <p>Authentication is working! The full app would load here.</p>
              <p>Google Client ID: {import.meta.env.VITE_GOOGLE_CLIENT_ID || "Not configured"}</p>
              <p>API URL: {import.meta.env.VITE_API_BASE_URL || "http://localhost:8000"}</p>
            </div>
          </GoogleSignedIn>
        </BrowserRouter>
      </GoogleAuthProvider>
    </ThemeProvider>
  );

  // Original complex app (commented out for debugging)
  /*
  try {
    return (
      <ScreenshotComponent>
        <ThemeProvider defaultTheme={{ base: "dark", color: "default" }}>
          <GoogleAuthProvider
            client={client}
            appName={import.meta.env.VITE_APP_NAME || "Veo 3 Prompt Architect"}
          >
            <BrowserRouter>
              <GoogleSignedOut>
                <GoogleLoginPage />
              </GoogleSignedOut>

              <GoogleSignedIn>
                <Routes>
                  <Route path="*" element={
                    <SystemErrorBoundary viewName="Fallback">
                      <Suspense fallback={<div>Loading...</div>}>
                        <Router />
                      </Suspense>
                    </SystemErrorBoundary>
                  } />
                </Routes>
              </GoogleSignedIn>
            </BrowserRouter>
          </GoogleAuthProvider>
        </ThemeProvider>
      </ScreenshotComponent>
    )
  } catch (error) {
    console.error('Root component error:', error);
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>Error Loading App</h1>
        <p>There was an error loading the application. Check the console for details.</p>
        <pre style={{ background: '#f0f0f0', padding: '10px', borderRadius: '5px' }}>
          {String(error)}
        </pre>
      </div>
    );
  }
  */
}

createRoot(document.getElementById("root")!).render(<Root />);