Collecting beautifulsoup4
  Downloading beautifulsoup4-4.13.4-py3-none-any.whl.metadata (3.8 kB)
Collecting boto3
  Downloading boto3-1.38.37-py3-none-any.whl.metadata (6.6 kB)
Collecting browser-use
  Downloading browser_use-0.2.7-py3-none-any.whl.metadata (10 kB)
Collecting fastapi
  Downloading fastapi-0.115.13-py3-none-any.whl.metadata (27 kB)
Collecting h11
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Collecting httpcore
  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Collecting httpx
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Collecting loguru
  Downloading loguru-0.7.3-py3-none-any.whl.metadata (22 kB)
Collecting psycopg
  Downloading psycopg-3.2.9-py3-none-any.whl.metadata (4.5 kB)
Collecting psycopg-pool
  Downloading psycopg_pool-3.2.6-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: pydantic in c:\users\<USER>\miniconda3\lib\site-packages (2.10.3)
Collecting pyjwt
  Downloading PyJWT-2.10.1-py3-none-any.whl.metadata (4.0 kB)
Collecting python-dotenv
  Downloading python_dotenv-1.1.0-py3-none-any.whl.metadata (24 kB)
Collecting python-multipart
  Using cached python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)
Requirement already satisfied: requests in c:\users\<USER>\miniconda3\lib\site-packages (2.32.3)
Collecting uvicorn
  Using cached uvicorn-0.34.3-py3-none-any.whl.metadata (6.5 kB)
Collecting soupsieve>1.2 (from beautifulsoup4)
  Downloading soupsieve-2.7-py3-none-any.whl.metadata (4.6 kB)
Requirement already satisfied: typing-extensions>=4.0.0 in c:\users\<USER>\miniconda3\lib\site-packages (from beautifulsoup4) (4.12.2)
Collecting botocore<1.39.0,>=1.38.37 (from boto3)
  Downloading botocore-1.38.37-py3-none-any.whl.metadata (5.7 kB)
Collecting jmespath<2.0.0,>=0.7.1 (from boto3)
  Downloading jmespath-1.0.1-py3-none-any.whl.metadata (7.6 kB)
Collecting s3transfer<0.14.0,>=0.13.0 (from boto3)
  Downloading s3transfer-0.13.0-py3-none-any.whl.metadata (1.7 kB)
Collecting aiofiles>=24.1.0 (from browser-use)
  Using cached aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)
Collecting anyio>=4.9.0 (from browser-use)
  Using cached anyio-4.9.0-py3-none-any.whl.metadata (4.7 kB)
Collecting faiss-cpu>=1.11.0 (from browser-use)
  Downloading faiss_cpu-1.11.0-cp313-cp313-win_amd64.whl.metadata (5.0 kB)
Collecting google-api-core>=2.25.0 (from browser-use)
  Downloading google_api_core-2.25.1-py3-none-any.whl.metadata (3.0 kB)
Collecting langchain-anthropic==0.3.15 (from browser-use)
  Downloading langchain_anthropic-0.3.15-py3-none-any.whl.metadata (1.9 kB)
Collecting langchain-aws>=0.2.24 (from browser-use)
  Downloading langchain_aws-0.2.26-py3-none-any.whl.metadata (3.2 kB)
Collecting langchain-core==0.3.64 (from browser-use)
  Downloading langchain_core-0.3.64-py3-none-any.whl.metadata (5.8 kB)
Collecting langchain-deepseek>=0.1.3 (from browser-use)
  Downloading langchain_deepseek-0.1.3-py3-none-any.whl.metadata (1.1 kB)
Collecting langchain-google-genai==2.1.5 (from browser-use)
  Downloading langchain_google_genai-2.1.5-py3-none-any.whl.metadata (5.2 kB)
Collecting langchain-ollama==0.3.3 (from browser-use)
  Downloading langchain_ollama-0.3.3-py3-none-any.whl.metadata (1.5 kB)
Collecting langchain-openai==0.3.21 (from browser-use)
  Downloading langchain_openai-0.3.21-py3-none-any.whl.metadata (2.3 kB)
Collecting langchain>=0.3.25 (from browser-use)
  Downloading langchain-0.3.25-py3-none-any.whl.metadata (7.8 kB)
Collecting markdownify==1.1.0 (from browser-use)
  Downloading markdownify-1.1.0-py3-none-any.whl.metadata (9.1 kB)
Collecting mem0ai>=0.1.106 (from browser-use)
  Downloading mem0ai-0.1.108-py3-none-any.whl.metadata (8.6 kB)
Collecting patchright>=1.52.5 (from browser-use)
  Downloading patchright-1.52.5-py3-none-win_amd64.whl.metadata (10 kB)
Collecting playwright>=1.52.0 (from browser-use)
  Downloading playwright-1.52.0-py3-none-win_amd64.whl.metadata (3.5 kB)
Collecting posthog>=3.7.0 (from browser-use)
  Downloading posthog-5.0.0-py3-none-any.whl.metadata (4.9 kB)
Collecting psutil>=7.0.0 (from browser-use)
  Downloading psutil-7.0.0-cp37-abi3-win_amd64.whl.metadata (23 kB)
Collecting pydantic
  Downloading pydantic-2.11.7-py3-none-any.whl.metadata (67 kB)
Collecting pyperclip>=1.9.0 (from browser-use)
  Downloading pyperclip-1.9.0.tar.gz (20 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting screeninfo>=0.8.1 (from browser-use)
  Downloading screeninfo-0.8.1-py3-none-any.whl.metadata (2.9 kB)
Collecting uuid7>=0.1.0 (from browser-use)
  Downloading uuid7-0.1.0-py2.py3-none-any.whl.metadata (3.6 kB)
Collecting anthropic<1,>=0.52.0 (from langchain-anthropic==0.3.15->browser-use)
  Downloading anthropic-0.54.0-py3-none-any.whl.metadata (25 kB)
Collecting langsmith<0.4,>=0.3.45 (from langchain-core==0.3.64->browser-use)
  Downloading langsmith-0.3.45-py3-none-any.whl.metadata (15 kB)
Collecting tenacity!=8.4.0,<10.0.0,>=8.1.0 (from langchain-core==0.3.64->browser-use)
  Downloading tenacity-9.1.2-py3-none-any.whl.metadata (1.2 kB)
Requirement already satisfied: jsonpatch<2.0,>=1.33 in c:\users\<USER>\miniconda3\lib\site-packages (from langchain-core==0.3.64->browser-use) (1.33)
Collecting PyYAML>=5.3 (from langchain-core==0.3.64->browser-use)
  Downloading PyYAML-6.0.2-cp313-cp313-win_amd64.whl.metadata (2.1 kB)
Requirement already satisfied: packaging<25,>=23.2 in c:\users\<USER>\miniconda3\lib\site-packages (from langchain-core==0.3.64->browser-use) (24.2)
Collecting filetype<2.0.0,>=1.2.0 (from langchain-google-genai==2.1.5->browser-use)
  Downloading filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)
Collecting google-ai-generativelanguage<0.7.0,>=0.6.18 (from langchain-google-genai==2.1.5->browser-use)
  Downloading google_ai_generativelanguage-0.6.18-py3-none-any.whl.metadata (9.8 kB)
Collecting ollama<1.0.0,>=0.4.8 (from langchain-ollama==0.3.3->browser-use)
  Downloading ollama-0.5.1-py3-none-any.whl.metadata (4.3 kB)
Collecting openai<2.0.0,>=1.68.2 (from langchain-openai==0.3.21->browser-use)
  Downloading openai-1.88.0-py3-none-any.whl.metadata (25 kB)
Collecting tiktoken<1,>=0.7 (from langchain-openai==0.3.21->browser-use)
  Downloading tiktoken-0.9.0-cp313-cp313-win_amd64.whl.metadata (6.8 kB)
Collecting six<2,>=1.15 (from markdownify==1.1.0->browser-use)
  Using cached six-1.17.0-py2.py3-none-any.whl.metadata (1.7 kB)
Collecting starlette<0.47.0,>=0.40.0 (from fastapi)
  Using cached starlette-0.46.2-py3-none-any.whl.metadata (6.2 kB)
Requirement already satisfied: certifi in c:\users\<USER>\miniconda3\lib\site-packages (from httpcore) (2025.4.26)
Requirement already satisfied: idna in c:\users\<USER>\miniconda3\lib\site-packages (from httpx) (3.7)
Requirement already satisfied: colorama>=0.3.4 in c:\users\<USER>\miniconda3\lib\site-packages (from loguru) (0.4.6)
Collecting win32-setctime>=1.0.0 (from loguru)
  Downloading win32_setctime-1.2.0-py3-none-any.whl.metadata (2.4 kB)
Collecting tzdata (from psycopg)
  Using cached tzdata-2025.2-py2.py3-none-any.whl.metadata (1.4 kB)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\miniconda3\lib\site-packages (from pydantic) (0.6.0)
Collecting pydantic-core==2.33.2 (from pydantic)
  Downloading pydantic_core-2.33.2-cp313-cp313-win_amd64.whl.metadata (6.9 kB)
Collecting typing-inspection>=0.4.0 (from pydantic)
  Using cached typing_inspection-0.4.1-py3-none-any.whl.metadata (2.6 kB)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\miniconda3\lib\site-packages (from requests) (3.3.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\miniconda3\lib\site-packages (from requests) (2.3.0)
Collecting click>=7.0 (from uvicorn)
  Using cached click-8.2.1-py3-none-any.whl.metadata (2.5 kB)
Collecting sniffio>=1.1 (from anyio>=4.9.0->browser-use)
  Using cached sniffio-1.3.1-py3-none-any.whl.metadata (3.9 kB)
Collecting python-dateutil<3.0.0,>=2.1 (from botocore<1.39.0,>=1.38.37->boto3)
  Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl.metadata (8.4 kB)
Collecting numpy<3.0,>=1.25.0 (from faiss-cpu>=1.11.0->browser-use)
  Downloading numpy-2.3.0-cp313-cp313-win_amd64.whl.metadata (60 kB)
Collecting googleapis-common-protos<2.0.0,>=1.56.2 (from google-api-core>=2.25.0->browser-use)
  Downloading googleapis_common_protos-1.70.0-py3-none-any.whl.metadata (9.3 kB)
Collecting protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.19.5 (from google-api-core>=2.25.0->browser-use)
  Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl.metadata (593 bytes)
Collecting proto-plus<2.0.0,>=1.22.3 (from google-api-core>=2.25.0->browser-use)
  Downloading proto_plus-1.26.1-py3-none-any.whl.metadata (2.2 kB)
Collecting google-auth<3.0.0,>=2.14.1 (from google-api-core>=2.25.0->browser-use)
  Downloading google_auth-2.40.3-py2.py3-none-any.whl.metadata (6.2 kB)
Collecting langchain-text-splitters<1.0.0,>=0.3.8 (from langchain>=0.3.25->browser-use)
  Downloading langchain_text_splitters-0.3.8-py3-none-any.whl.metadata (1.9 kB)
Collecting SQLAlchemy<3,>=1.4 (from langchain>=0.3.25->browser-use)
  Downloading sqlalchemy-2.0.41-cp313-cp313-win_amd64.whl.metadata (9.8 kB)
INFO: pip is looking at multiple versions of langchain-aws to determine which version is compatible with other requirements. This could take a while.
Collecting langchain-aws>=0.2.24 (from browser-use)
  Downloading langchain_aws-0.2.25-py3-none-any.whl.metadata (3.2 kB)
Collecting pytz>=2024.1 (from mem0ai>=0.1.106->browser-use)
  Using cached pytz-2025.2-py2.py3-none-any.whl.metadata (22 kB)
Collecting qdrant-client>=1.9.1 (from mem0ai>=0.1.106->browser-use)
  Downloading qdrant_client-1.14.3-py3-none-any.whl.metadata (10 kB)
Collecting pyee<14,>=13 (from patchright>=1.52.5->browser-use)
  Downloading pyee-13.0.0-py3-none-any.whl.metadata (2.9 kB)
Collecting greenlet<4.0.0,>=3.1.1 (from patchright>=1.52.5->browser-use)
  Downloading greenlet-3.2.3-cp313-cp313-win_amd64.whl.metadata (4.2 kB)
Collecting backoff>=1.10.0 (from posthog>=3.7.0->browser-use)
  Downloading backoff-2.2.1-py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: distro>=1.5.0 in c:\users\<USER>\miniconda3\lib\site-packages (from posthog>=3.7.0->browser-use) (1.9.0)
Collecting jiter<1,>=0.4.0 (from anthropic<1,>=0.52.0->langchain-anthropic==0.3.15->browser-use)
  Downloading jiter-0.10.0-cp313-cp313-win_amd64.whl.metadata (5.3 kB)
Collecting cachetools<6.0,>=2.0.0 (from google-auth<3.0.0,>=2.14.1->google-api-core>=2.25.0->browser-use)
  Downloading cachetools-5.5.2-py3-none-any.whl.metadata (5.4 kB)
Collecting pyasn1-modules>=0.2.1 (from google-auth<3.0.0,>=2.14.1->google-api-core>=2.25.0->browser-use)
  Downloading pyasn1_modules-0.4.2-py3-none-any.whl.metadata (3.5 kB)
Collecting rsa<5,>=3.1.4 (from google-auth<3.0.0,>=2.14.1->google-api-core>=2.25.0->browser-use)
  Downloading rsa-4.9.1-py3-none-any.whl.metadata (5.6 kB)
Requirement already satisfied: jsonpointer>=1.9 in c:\users\<USER>\miniconda3\lib\site-packages (from jsonpatch<2.0,>=1.33->langchain-core==0.3.64->browser-use) (2.1)
Collecting orjson<4.0.0,>=3.9.14 (from langsmith<0.4,>=0.3.45->langchain-core==0.3.64->browser-use)
  Downloading orjson-3.10.18-cp313-cp313-win_amd64.whl.metadata (43 kB)
Collecting requests-toolbelt<2.0.0,>=1.0.0 (from langsmith<0.4,>=0.3.45->langchain-core==0.3.64->browser-use)
  Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl.metadata (14 kB)
Requirement already satisfied: zstandard<0.24.0,>=0.23.0 in c:\users\<USER>\miniconda3\lib\site-packages (from langsmith<0.4,>=0.3.45->langchain-core==0.3.64->browser-use) (0.23.0)
Requirement already satisfied: tqdm>4 in c:\users\<USER>\miniconda3\lib\site-packages (from openai<2.0.0,>=1.68.2->langchain-openai==0.3.21->browser-use) (4.67.1)
Collecting grpcio>=1.41.0 (from qdrant-client>=1.9.1->mem0ai>=0.1.106->browser-use)
  Downloading grpcio-1.73.0-cp313-cp313-win_amd64.whl.metadata (4.0 kB)
Collecting portalocker<3.0.0,>=2.7.0 (from qdrant-client>=1.9.1->mem0ai>=0.1.106->browser-use)
  Downloading portalocker-2.10.1-py3-none-any.whl.metadata (8.5 kB)
Collecting regex>=2022.1.18 (from tiktoken<1,>=0.7->langchain-openai==0.3.21->browser-use)
  Downloading regex-2024.11.6-cp313-cp313-win_amd64.whl.metadata (41 kB)
Collecting grpcio-status<2.0.0,>=1.33.2 (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0,>=1.34.1->google-ai-generativelanguage<0.7.0,>=0.6.18->langchain-google-genai==2.1.5->browser-use)
  Downloading grpcio_status-1.73.0-py3-none-any.whl.metadata (1.1 kB)
Collecting h2<5,>=3 (from httpx[http2]>=0.20.0->qdrant-client>=1.9.1->mem0ai>=0.1.106->browser-use)
  Downloading h2-4.2.0-py3-none-any.whl.metadata (5.1 kB)
Collecting pywin32>=226 (from portalocker<3.0.0,>=2.7.0->qdrant-client>=1.9.1->mem0ai>=0.1.106->browser-use)
  Downloading pywin32-310-cp313-cp313-win_amd64.whl.metadata (9.4 kB)
Collecting pyasn1<0.7.0,>=0.6.1 (from pyasn1-modules>=0.2.1->google-auth<3.0.0,>=2.14.1->google-api-core>=2.25.0->browser-use)
  Downloading pyasn1-0.6.1-py3-none-any.whl.metadata (8.4 kB)
Collecting hyperframe<7,>=6.1 (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client>=1.9.1->mem0ai>=0.1.106->browser-use)
  Downloading hyperframe-6.1.0-py3-none-any.whl.metadata (4.3 kB)
Collecting hpack<5,>=4.1 (from h2<5,>=3->httpx[http2]>=0.20.0->qdrant-client>=1.9.1->mem0ai>=0.1.106->browser-use)
  Downloading hpack-4.1.0-py3-none-any.whl.metadata (4.6 kB)
Downloading beautifulsoup4-4.13.4-py3-none-any.whl (187 kB)
Downloading boto3-1.38.37-py3-none-any.whl (139 kB)
Downloading browser_use-0.2.7-py3-none-any.whl (172 kB)
Downloading langchain_anthropic-0.3.15-py3-none-any.whl (28 kB)
Downloading langchain_core-0.3.64-py3-none-any.whl (438 kB)
Downloading langchain_google_genai-2.1.5-py3-none-any.whl (44 kB)
Downloading langchain_ollama-0.3.3-py3-none-any.whl (21 kB)
Downloading langchain_openai-0.3.21-py3-none-any.whl (65 kB)
Downloading markdownify-1.1.0-py3-none-any.whl (13 kB)
Downloading fastapi-0.115.13-py3-none-any.whl (95 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Downloading loguru-0.7.3-py3-none-any.whl (61 kB)
Downloading psycopg-3.2.9-py3-none-any.whl (202 kB)
Downloading psycopg_pool-3.2.6-py3-none-any.whl (38 kB)
Downloading pydantic-2.11.7-py3-none-any.whl (444 kB)
Downloading pydantic_core-2.33.2-cp313-cp313-win_amd64.whl (2.0 MB)
   ------------------------ 2.0/2.0 MB 64.5 MB/s eta 0:00:00
Downloading PyJWT-2.10.1-py3-none-any.whl (22 kB)
Downloading python_dotenv-1.1.0-py3-none-any.whl (20 kB)
Using cached python_multipart-0.0.20-py3-none-any.whl (24 kB)
Using cached uvicorn-0.34.3-py3-none-any.whl (62 kB)
Using cached aiofiles-24.1.0-py3-none-any.whl (15 kB)
Using cached anyio-4.9.0-py3-none-any.whl (100 kB)
Downloading botocore-1.38.37-py3-none-any.whl (13.6 MB)
   --------------------- 13.6/13.6 MB 128.2 MB/s eta 0:00:00
Using cached click-8.2.1-py3-none-any.whl (102 kB)
Downloading faiss_cpu-1.11.0-cp313-cp313-win_amd64.whl (15.0 MB)
   --------------------- 15.0/15.0 MB 128.3 MB/s eta 0:00:00
Downloading google_api_core-2.25.1-py3-none-any.whl (160 kB)
Downloading jmespath-1.0.1-py3-none-any.whl (20 kB)
Downloading langchain-0.3.25-py3-none-any.whl (1.0 MB)
   ------------------------ 1.0/1.0 MB 89.4 MB/s eta 0:00:00
Downloading langchain_aws-0.2.25-py3-none-any.whl (120 kB)
Downloading langchain_deepseek-0.1.3-py3-none-any.whl (7.1 kB)
Downloading mem0ai-0.1.108-py3-none-any.whl (161 kB)
Downloading patchright-1.52.5-py3-none-win_amd64.whl (34.8 MB)
   ---------------------- 34.8/34.8 MB 70.0 MB/s eta 0:00:00
Downloading playwright-1.52.0-py3-none-win_amd64.whl (34.8 MB)
   --------------------- 34.8/34.8 MB 129.2 MB/s eta 0:00:00
Downloading posthog-5.0.0-py3-none-any.whl (100 kB)
Downloading psutil-7.0.0-cp37-abi3-win_amd64.whl (244 kB)
Downloading s3transfer-0.13.0-py3-none-any.whl (85 kB)
Downloading screeninfo-0.8.1-py3-none-any.whl (12 kB)
Downloading soupsieve-2.7-py3-none-any.whl (36 kB)
Using cached starlette-0.46.2-py3-none-any.whl (72 kB)
Using cached typing_inspection-0.4.1-py3-none-any.whl (14 kB)
Downloading uuid7-0.1.0-py2.py3-none-any.whl (7.5 kB)
Downloading win32_setctime-1.2.0-py3-none-any.whl (4.1 kB)
Using cached tzdata-2025.2-py2.py3-none-any.whl (347 kB)
Downloading anthropic-0.54.0-py3-none-any.whl (288 kB)
Downloading backoff-2.2.1-py3-none-any.whl (15 kB)
Downloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)
Downloading google_ai_generativelanguage-0.6.18-py3-none-any.whl (1.4 MB)
   ------------------------ 1.4/1.4 MB 31.2 MB/s eta 0:00:00
Downloading google_auth-2.40.3-py2.py3-none-any.whl (216 kB)
Downloading googleapis_common_protos-1.70.0-py3-none-any.whl (294 kB)
Downloading greenlet-3.2.3-cp313-cp313-win_amd64.whl (297 kB)
Downloading langchain_text_splitters-0.3.8-py3-none-any.whl (32 kB)
Downloading langsmith-0.3.45-py3-none-any.whl (363 kB)
Downloading numpy-2.3.0-cp313-cp313-win_amd64.whl (12.7 MB)
   --------------------- 12.7/12.7 MB 128.6 MB/s eta 0:00:00
Downloading ollama-0.5.1-py3-none-any.whl (13 kB)
Downloading openai-1.88.0-py3-none-any.whl (734 kB)
   ------------------- 734.3/734.3 kB 101.6 MB/s eta 0:00:00
Downloading proto_plus-1.26.1-py3-none-any.whl (50 kB)
Using cached protobuf-6.31.1-cp310-abi3-win_amd64.whl (435 kB)
Downloading pyee-13.0.0-py3-none-any.whl (15 kB)
Using cached python_dateutil-2.9.0.post0-py2.py3-none-any.whl (229 kB)
Using cached pytz-2025.2-py2.py3-none-any.whl (509 kB)
Downloading PyYAML-6.0.2-cp313-cp313-win_amd64.whl (156 kB)
Downloading qdrant_client-1.14.3-py3-none-any.whl (328 kB)
Using cached six-1.17.0-py2.py3-none-any.whl (11 kB)
Using cached sniffio-1.3.1-py3-none-any.whl (10 kB)
Downloading sqlalchemy-2.0.41-cp313-cp313-win_amd64.whl (2.1 MB)
   ----------------------- 2.1/2.1 MB 115.5 MB/s eta 0:00:00
Downloading tenacity-9.1.2-py3-none-any.whl (28 kB)
Downloading tiktoken-0.9.0-cp313-cp313-win_amd64.whl (894 kB)
   ------------------- 894.7/894.7 kB 109.1 MB/s eta 0:00:00
Downloading cachetools-5.5.2-py3-none-any.whl (10 kB)
Downloading grpcio-1.73.0-cp313-cp313-win_amd64.whl (4.3 MB)
   ----------------------- 4.3/4.3 MB 124.5 MB/s eta 0:00:00
Downloading jiter-0.10.0-cp313-cp313-win_amd64.whl (205 kB)
Downloading orjson-3.10.18-cp313-cp313-win_amd64.whl (134 kB)
Downloading portalocker-2.10.1-py3-none-any.whl (18 kB)
Downloading pyasn1_modules-0.4.2-py3-none-any.whl (181 kB)
Downloading regex-2024.11.6-cp313-cp313-win_amd64.whl (273 kB)
Downloading requests_toolbelt-1.0.0-py2.py3-none-any.whl (54 kB)
Downloading rsa-4.9.1-py3-none-any.whl (34 kB)
Downloading grpcio_status-1.73.0-py3-none-any.whl (14 kB)
Downloading h2-4.2.0-py3-none-any.whl (60 kB)
Downloading pyasn1-0.6.1-py3-none-any.whl (83 kB)
Downloading pywin32-310-cp313-cp313-win_amd64.whl (9.5 MB)
   ----------------------- 9.5/9.5 MB 127.5 MB/s eta 0:00:00
Downloading hpack-4.1.0-py3-none-any.whl (34 kB)
Downloading hyperframe-6.1.0-py3-none-any.whl (13 kB)
Building wheels for collected packages: pyperclip
  Building wheel for pyperclip (setup.py): started
  Building wheel for pyperclip (setup.py): finished with status 'done'
  Created wheel for pyperclip: filename=pyperclip-1.9.0-py3-none-any.whl size=11117 sha256=1e046a45108c6d60e2377ec358fd7596bc96169379b3d7ba917e735188e2bbcb
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\9c\79\90\c9e831caaffa2633ad99f1d35c6ea39866b92de62e909e89ef
Successfully built pyperclip
Installing collected packages: pywin32, pytz, pyperclip, filetype, win32-setctime, uuid7, tzdata, typing-inspection, tenacity, soupsieve, sniffio, six, screeninfo, regex, PyYAML, python-multipart, python-dotenv, pyjwt, pyee, pydantic-core, pyasn1, psycopg-pool, psutil, protobuf, portalocker, orjson, numpy, jmespath, jiter, hyperframe, hpack, h11, grpcio, greenlet, click, cachetools, backoff, aiofiles, uvicorn, tiktoken, SQLAlchemy, rsa, requests-toolbelt, python-dateutil, pydantic, pyasn1-modules, psycopg, proto-plus, playwright, patchright, loguru, httpcore, h2, googleapis-common-protos, faiss-cpu, beautifulsoup4, anyio, starlette, posthog, markdownify, httpx, grpcio-status, google-auth, botocore, s3transfer, openai, ollama, langsmith, google-api-core, fastapi, anthropic, qdrant-client, langchain-core, boto3, mem0ai, langchain-text-splitters, langchain-openai, langchain-ollama, langchain-aws, langchain-anthropic, google-ai-generativelanguage, langchain-google-genai, langchain-deepseek, langchain, browser-use
  Attempting uninstall: pydantic-core
    Found existing installation: pydantic_core 2.27.1
    Uninstalling pydantic_core-2.27.1:
      Successfully uninstalled pydantic_core-2.27.1
  Attempting uninstall: pydantic
    Found existing installation: pydantic 2.10.3
    Uninstalling pydantic-2.10.3:
      Successfully uninstalled pydantic-2.10.3
Successfully installed PyYAML-6.0.2 SQLAlchemy-2.0.41 aiofiles-24.1.0 anthropic-0.54.0 anyio-4.9.0 backoff-2.2.1 beautifulsoup4-4.13.4 boto3-1.38.37 botocore-1.38.37 browser-use-0.2.7 cachetools-5.5.2 click-8.2.1 faiss-cpu-1.11.0 fastapi-0.115.13 filetype-1.2.0 google-ai-generativelanguage-0.6.18 google-api-core-2.25.1 google-auth-2.40.3 googleapis-common-protos-1.70.0 greenlet-3.2.3 grpcio-1.73.0 grpcio-status-1.73.0 h11-0.16.0 h2-4.2.0 hpack-4.1.0 httpcore-1.0.9 httpx-0.28.1 hyperframe-6.1.0 jiter-0.10.0 jmespath-1.0.1 langchain-0.3.25 langchain-anthropic-0.3.15 langchain-aws-0.2.25 langchain-core-0.3.64 langchain-deepseek-0.1.3 langchain-google-genai-2.1.5 langchain-ollama-0.3.3 langchain-openai-0.3.21 langchain-text-splitters-0.3.8 langsmith-0.3.45 loguru-0.7.3 markdownify-1.1.0 mem0ai-0.1.108 numpy-2.3.0 ollama-0.5.1 openai-1.88.0 orjson-3.10.18 patchright-1.52.5 playwright-1.52.0 portalocker-2.10.1 posthog-5.0.0 proto-plus-1.26.1 protobuf-6.31.1 psutil-7.0.0 psycopg-3.2.9 psycopg-pool-3.2.6 pyasn1-0.6.1 pyasn1-modules-0.4.2 pydantic-2.11.7 pydantic-core-2.33.2 pyee-13.0.0 pyjwt-2.10.1 pyperclip-1.9.0 python-dateutil-2.9.0.post0 python-dotenv-1.1.0 python-multipart-0.0.20 pytz-2025.2 pywin32-310 qdrant-client-1.14.3 regex-2024.11.6 requests-toolbelt-1.0.0 rsa-4.9.1 s3transfer-0.13.0 screeninfo-0.8.1 six-1.17.0 sniffio-1.3.1 soupsieve-2.7 starlette-0.46.2 tenacity-9.1.2 tiktoken-0.9.0 typing-inspection-0.4.1 tzdata-2025.2 uuid7-0.1.0 uvicorn-0.34.3 win32-setctime-1.2.0
