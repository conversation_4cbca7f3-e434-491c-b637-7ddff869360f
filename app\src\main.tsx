
import './logger.ts';
import { createRoot } from "react-dom/client";
import "./index.css";
import { ThemeProvider } from "./components/ThemeProvider.tsx";
import { AppLayout } from "./components/layout/AppLayout.tsx";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { client } from './lib/sdk/client.gen';

client.setConfig({
  baseUrl: import.meta.env.VITE_API_BASE_URL || "http://localhost:8000",
});

// Simplified PromptArchitect component for testing
const SimplePromptArchitect = () => {
  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-2">Veo 3 Prompt Architect</h1>
        <p className="text-muted-foreground">Generate structured prompts for Google's Veo video generation model</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>1. Core Scene Elements</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="subjects">Subject(s)</Label>
            <Input
              id="subjects"
              placeholder="e.g., A grizzled detective in a trench coat"
            />
          </div>
          <div>
            <Label htmlFor="action">Action</Label>
            <Input
              id="action"
              placeholder="e.g., sprinting through a crowded market"
            />
          </div>
          <div>
            <Label htmlFor="environment">Environment & Setting</Label>
            <Textarea
              id="environment"
              placeholder="e.g., A desolate, rain-slicked alleyway in a cyberpunk city at midnight"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>2. Cinematic Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">Camera angles, lighting, and visual aesthetic controls would go here.</p>
          <Button className="w-full" size="lg">
            Generate Prompt
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

const Root = () => {
  console.log('Root component rendering...');
  Element.prototype.scrollIntoView = function() { return false; };
  Element.prototype.scrollTo = function() { return false; };
  Element.prototype.scrollBy = function() { return false; };

  return (
    <ThemeProvider defaultTheme={{ base: "dark", color: "default" }}>
      <AppLayout>
        <SimplePromptArchitect />
      </AppLayout>
    </ThemeProvider>
  );

  // Original complex app (commented out for debugging)
  /*
  try {
    return (
      <ScreenshotComponent>
        <ThemeProvider defaultTheme={{ base: "dark", color: "default" }}>
          <GoogleAuthProvider
            client={client}
            appName={import.meta.env.VITE_APP_NAME || "Veo 3 Prompt Architect"}
          >
            <BrowserRouter>
              <GoogleSignedOut>
                <GoogleLoginPage />
              </GoogleSignedOut>

              <GoogleSignedIn>
                <Routes>
                  <Route path="*" element={
                    <SystemErrorBoundary viewName="Fallback">
                      <Suspense fallback={<div>Loading...</div>}>
                        <Router />
                      </Suspense>
                    </SystemErrorBoundary>
                  } />
                </Routes>
              </GoogleSignedIn>
            </BrowserRouter>
          </GoogleAuthProvider>
        </ThemeProvider>
      </ScreenshotComponent>
    )
  } catch (error) {
    console.error('Root component error:', error);
    return (
      <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
        <h1>Error Loading App</h1>
        <p>There was an error loading the application. Check the console for details.</p>
        <pre style={{ background: '#f0f0f0', padding: '10px', borderRadius: '5px' }}>
          {String(error)}
        </pre>
      </div>
    );
  }
  */
}

createRoot(document.getElementById("root")!).render(<Root />);