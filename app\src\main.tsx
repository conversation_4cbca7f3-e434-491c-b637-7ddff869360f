
import './logger.ts';
import { StrictMode, useEffect, useState, useRef, createContext, lazy, Suspense } from "react";
import { createRoot } from "react-dom/client";
import { BrowserRouter, Routes, Route } from "react-router-dom";

import Fallback from "./Fallback.tsx";
import SystemErrorBoundary from "./SystemErrorBoundary.tsx";
import "./index.css";
import ScreenshotComponent from "./components/ScreenshotComponent.tsx";
import Router from "./components/Router.tsx";
import { GoogleAuthProvider, GoogleSignedIn, GoogleSignedOut } from "./auth/GoogleAuthProvider.tsx";
import { GoogleLoginPage } from "./auth/GoogleLoginPage.tsx";
import { ThemeProvider } from "./components/ThemeProvider.tsx";
import { client } from './lib/sdk/client.gen';

client.setConfig({
  baseUrl: import.meta.env.VITE_API_BASE_URL || "http://localhost:8000",
});

export const AuthTokenContext = createContext<string | null>(null);

// Simple test component to verify React is working
const TestApp = () => {
  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🎉 React App is Working!</h1>
      <p>Backend URL: {import.meta.env.VITE_API_BASE_URL || "http://localhost:8000"}</p>
      <p>Environment: {import.meta.env.MODE}</p>
      <p>Google Client ID: {import.meta.env.VITE_GOOGLE_CLIENT_ID || "Not configured"}</p>
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f0f0f0', borderRadius: '5px' }}>
        <h3>Next Steps:</h3>
        <ul>
          <li>✅ Frontend is running</li>
          <li>✅ Backend is running</li>
          <li>⚠️ Google authentication needs configuration</li>
        </ul>
      </div>
    </div>
  );
};

const Root = () => {
  Element.prototype.scrollIntoView = function() { return false; };
  Element.prototype.scrollTo = function() { return false; };
  Element.prototype.scrollBy = function() { return false; };

  // For now, show the test app to verify everything is working
  return <TestApp />;

  // Original app with authentication (commented out for now)
  /*
  return (
    <ThemeProvider defaultTheme={{ base: "dark", color: "default" }}>
      <GoogleAuthProvider
        client={client}
        appName={import.meta.env.VITE_APP_NAME || "Veo 3 Prompt Architect"}
      >
        <BrowserRouter>
          <GoogleSignedOut>
            <GoogleLoginPage />
          </GoogleSignedOut>

          <GoogleSignedIn>
            <Routes>
              <Route path="*" element={
                <SystemErrorBoundary viewName="Fallback">
                  <Suspense fallback={<div>Loading...</div>}>
                    <Router />
                  </Suspense>
                </SystemErrorBoundary>
              } />
            </Routes>
          </GoogleSignedIn>
        </BrowserRouter>
        <ScreenshotComponent />
      </GoogleAuthProvider>
    </ThemeProvider>
  )
  */
}

createRoot(document.getElementById("root")!).render(<Root />);