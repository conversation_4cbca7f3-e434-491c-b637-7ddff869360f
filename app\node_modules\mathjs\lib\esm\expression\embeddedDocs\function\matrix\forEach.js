export var forEachDocs = {
  name: 'forEach',
  category: 'Matrix',
  syntax: ['forEach(x, callback)'],
  description: 'Iterates over all elements of a matrix/array, and executes the given callback function.',
  examples: ['numberOfPets = {}', 'addPet(n) = numberOfPets[n] = (numberOfPets[n] ? numberOfPets[n]:0 ) + 1;', 'forEach(["<PERSON>","<PERSON>","Cat"], addPet)', 'numberOfPets'],
  seealso: ['map', 'sort', 'filter']
};